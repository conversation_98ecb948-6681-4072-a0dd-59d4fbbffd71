import {
  fbPartialsCollection,
  PlaybookCodeCell,
} from "../markdown/notebook/mod.ts";
import { isAsyncIterator } from "../universal/collectable.ts";
import {
  hexOfUint8,
  hexOfUint8Postgres,
  literal,
} from "../universal/sql-text.ts";
import { safeJsonStringify } from "../universal/tmpl-literal-aide.ts";
import { SqlPageProvenance } from "./playbook.ts";
import { PageRoute, RouteSupplier } from "./route.ts";

export type SqlPagePath =
  & {
    readonly path: string;
    readonly sql: string; // usually '${path}'
    readonly absURL: () => string; // usually (COALESCE(sqlpage.environment_variable('SQLPAGE_SITE_PREFIX'), '') || '${path}')
    readonly homePath: () => string; // usually ('${path}' || '/index.sql')
    readonly isRoute: boolean;
  }
  & (
    | {
      readonly nature: "route";
      readonly isRoute: true;
    } & RouteSupplier
    | {
      readonly nature: "path";
      readonly isRoute: false;
    }
  );

export function sqlPagePath(candidate: string | PageRoute): SqlPagePath {
  const sql = literal(
    typeof candidate === "string" ? candidate : candidate.path,
  );
  const absURL = () =>
    `(COALESCE(sqlpage.environment_variable('SQLPAGE_SITE_PREFIX'), '') || ${sql})`;
  const homePath = () => `(${sql} || '/index.sql')`;

  if (typeof candidate === "string") {
    return {
      nature: "path",
      isRoute: false,
      path: candidate,
      sql,
      absURL,
      homePath,
    };
  } else {
    return {
      nature: "route",
      isRoute: true,
      route: candidate,
      sql,
      path: candidate.path,
      absURL,
      homePath,
    };
  }
}

export function sqlPagePathsFactory() {
  const encountered = new Map<string, SqlPagePath>();
  return {
    sqlPagePath: (candidate: Parameters<typeof sqlPagePath>[0]) => {
      const key = typeof candidate === "string" ? candidate : candidate.path;
      let spp = encountered.get(key);
      if (!spp) {
        spp = sqlPagePath(candidate);
        encountered.set(key, spp);
      }
      return spp;
    },
  };
}

type InjectableMatch = Awaited<
  ReturnType<ReturnType<typeof fbPartialsCollection>["findInjectableForPath"]>
>;

/** Common fields present on all SqlPageFile variants */
type SqlPageFileBase = {
  /** Discriminator */
  readonly kind: "head_sql" | "tail_sql" | "sqlpage_file_upsert";
  /** Relative path (e.g., "sql.d/head/001.sql", "admin/index.sql") */
  readonly path: string;
  /** File contents (originates from cell.source, may be mutated for interpolations) */
  contents: string | Uint8Array | ReadableStream<Uint8Array>;
  /** Optional timestamp (not used in DML; engine time is used) */
  readonly lastModified?: Date;
  /** The notebook/playbook cell this file originated from, if any */
  readonly cell?: PlaybookCodeCell<SqlPageProvenance>;
};

/** Minimal variant for static head/tail SQL files */
export type SqlPageHeadOrTail =
  & SqlPageFileBase
  & {
    readonly kind: "head_sql" | "tail_sql";
    // No extra fields allowed here — intentionally minimal
  };

/** Extended variant for files that are upserted/generated by the engine */
export type SqlPageFileUpsert =
  & SqlPageFileBase
  & {
    readonly kind: "sqlpage_file_upsert";
    readonly asErrorContents: (text: string, error: unknown) => string;
    readonly isBinary: false | ReadableStream<Uint8Array>;
    readonly isAutoGenerated?: boolean;
    readonly isPartial?: boolean;
    readonly isUnsafeInterpolatable?: boolean;
    isInterpolated?: boolean;
    readonly isInjectableCandidate?: boolean;
    partialInjected?: InjectableMatch;
    error?: unknown;
  };

/** Discriminated union */
export type SqlPageContent = SqlPageHeadOrTail | SqlPageFileUpsert;

/** Wrapper type for contributors */
export type SqlPageFileContributor = {
  readonly sqlPageFile: SqlPageContent;
};

/** Narrow to any SqlPageFile */
export function isSqlPageContent(o: unknown): o is SqlPageContent {
  if (!o || typeof o !== "object") return false;
  const a = o as Partial<SqlPageContent>;
  return typeof a.kind === "string" && typeof a.path === "string" &&
    "contents" in a;
}

/** Narrow to the minimal head/tail variant */
export function isSqlPageHeadOrTail(o: SqlPageContent): o is SqlPageHeadOrTail {
  return o.kind === "head_sql" || o.kind === "tail_sql";
}

/** Narrow to the extended upsert variant */
export function isSqlPageFileUpsert(o: SqlPageContent): o is SqlPageFileUpsert {
  return o.kind === "sqlpage_file_upsert";
}

/** Narrow contributor wrapper */
export function isSqlPageFileContributor(
  o: unknown,
): o is SqlPageFileContributor {
  return !!o && typeof o === "object" && "sqlPageFile" in o &&
    isSqlPageContent(o.sqlPageFile);
}

export function sqlPageContentHelpers() {
  const sql = (
    path: string,
    contents: string,
    candidate?: Partial<SqlPageContent>,
  ): SqlPageContent => ({
    kind: candidate?.kind ?? "sqlpage_file_upsert",
    path,
    contents,
    asErrorContents: (text) => text.replaceAll(/^/gm, "-- "),
    isBinary: false,
    ...candidate,
  });

  const json = (
    path: string,
    contents: string,
    candidate?: Partial<SqlPageContent>,
  ): SqlPageContent => ({
    kind: candidate?.kind ?? "sqlpage_file_upsert",
    path,
    contents,
    asErrorContents: (text, error) => safeJsonStringify({ text, error }),
    isBinary: false,
    ...candidate,
  });

  return { sql, json };
}

export type SqlPageContentStream =
  | SqlPageContent
  | AsyncIterable<SqlPageContent>
  | AsyncIterator<SqlPageContent>;

export async function* normalizeSPC(
  input: SqlPageContentStream,
): AsyncIterable<SqlPageContent> {
  // Single Source object
  if (isSqlPageContent(input)) {
    yield input;
  }

  // Async iterator / async iterable of Source
  const it = isAsyncIterator(input)
    ? (input as AsyncIterator<SqlPageContentStream>)
    : (input as AsyncIterable<SqlPageContentStream>)[Symbol.asyncIterator]();

  while (true) {
    const { value, done } = await it.next();
    if (done) break;
    if (!isSqlPageContent(value)) {
      throw new TypeError("Stream yielded a non-SqlPageContent value");
    }
    yield value;
  }
}

export enum SqlPageFilesUpsertDialect {
  SQLite = "sqlite",
  PostgreSQL = "postgres",
}

/**
 * Build DML statements to upsert files into a SQLPage virtual-files table.
 * dialect "sqlite":
 *   INSERT INTO sqlpage_files (path, contents, last_modified) VALUES ('…','…', CURRENT_TIMESTAMP)
 *   ON CONFLICT(path) DO UPDATE
 *     SET contents = excluded.contents,
 *         last_modified = CURRENT_TIMESTAMP
 *     WHERE sqlpage_files.contents <> excluded.contents;
 *
 * Returns one object per file, tagged with kind: "sqlpage_file_insert".
 * On conflict when contents differ, last_modified is set by the SQL engine (CURRENT_TIMESTAMP).
 * If contents are identical, the row is left unchanged.
 */
export async function sqlPageFilesUpsertDML(
  spcStream: SqlPageContentStream,
  opts: {
    dialect: SqlPageFilesUpsertDialect;
    includeSqlPageFilesTable?: boolean;
  },
) {
  if (
    opts.dialect !== SqlPageFilesUpsertDialect.SQLite &&
    opts.dialect !== SqlPageFilesUpsertDialect.PostgreSQL
  ) {
    throw new Error(`Unsupported dialect: ${opts.dialect}`);
  }

  const esc = (s: string) => s.replaceAll("'", "''");
  const quoted = async (
    s: string | Uint8Array | ReadableStream<Uint8Array>,
  ): Promise<string> => {
    if (typeof s === "string") return `'${esc(s)}'`;
    if (s instanceof Uint8Array) {
      return opts.dialect === SqlPageFilesUpsertDialect.PostgreSQL
        ? hexOfUint8Postgres(s)
        : hexOfUint8(s);
    }

    const reader = s.getReader();
    const chunks: Uint8Array[] = [];
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      if (value) chunks.push(value);
    }
    const bytes = new Uint8Array(chunks.reduce((n, c) => n + c.length, 0));
    let offset = 0;
    for (const c of chunks) {
      bytes.set(c, offset);
      offset += c.length;
    }
    return opts.dialect === SqlPageFilesUpsertDialect.PostgreSQL
      ? hexOfUint8Postgres(bytes)
      : hexOfUint8(bytes);
  };

  const list = await Array.fromAsync(normalizeSPC(spcStream));

  const headSql = list.filter((e) => e.kind === "head_sql").map((spf) =>
    spf.contents
  );
  const tailSql = list.filter((e) => e.kind === "tail_sql").map((spf) =>
    spf.contents
  );

  const upserts = await Promise.all(
    list
      .filter((e) => e.kind === "sqlpage_file_upsert")
      .map(async (f) => {
        const pathLit = `'${esc(f.path)}'`;
        const bodyLit = await quoted(f.contents);
        return `INSERT INTO sqlpage_files (path, contents, last_modified) VALUES (${pathLit}, ${
          (opts.dialect === SqlPageFilesUpsertDialect.PostgreSQL &&
              typeof f.contents === "string")
            ? `convert_to(${bodyLit}, 'UTF8')`
            : bodyLit
        }, CURRENT_TIMESTAMP) ` +
          `ON CONFLICT(path) DO UPDATE SET contents = excluded.contents, last_modified = CURRENT_TIMESTAMP ` +
          `WHERE sqlpage_files.contents <> excluded.contents;`;
      }),
  );

  return [
    opts.includeSqlPageFilesTable
      ? `CREATE TABLE IF NOT EXISTS "sqlpage_files" ("path" VARCHAR PRIMARY KEY NOT NULL, "contents" ${
        opts.dialect === SqlPageFilesUpsertDialect.PostgreSQL ? "BYTEA" : "TEXT"
      } NOT NULL, "last_modified" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP);`
      : "-- sqlpage_files DDL not requested",
    ...headSql,
    ...upserts,
    ...tailSql,
  ];
}
