import { Command, EnumType } from "jsr:@cliffy/command@1.0.0-rc.8";
import { CompletionsCommand } from "jsr:@cliffy/command@1.0.0-rc.8/completions";
import { HelpCommand } from "jsr:@cliffy/command@1.0.0-rc.8/help";
import {
  bold,
  brightYellow,
  cyan,
  gray,
  green,
  red,
  yellow,
} from "jsr:@std/fmt@^1/colors";
import { ensureDir } from "jsr:@std/fs@^1";
import {
  basename,
  dirname,
  fromFileUrl,
  globToRegExp,
  join,
  relative,
} from "jsr:@std/path@^1";
import { MarkdownDoc } from "../markdown/fluent-doc.ts";
import { compileCqlMini } from "../markdown/notebook/cql.ts";
import * as taskCLI from "../task/cli.ts";
import { execTasksState, gitignorableOnCapture } from "../task/execute.ts";
import { markdownShellEventBus } from "../task/mdbus.ts";
import { executeTasks, TaskCell, TaskExecContext } from "../task/mod.ts";
import { collectAsyncGenerated } from "../universal/collectable.ts";
import { SourceRelativeTo } from "../universal/content-acquisition.ts";
import { doctor } from "../universal/doctor.ts";
import { eventBus } from "../universal/event-bus.ts";
import { gitignore } from "../universal/gitignore.ts";
import { ColumnDef, ListerBuilder } from "../universal/lister-tabular-tui.ts";
import { TreeLister } from "../universal/lister-tree-tui.ts";
import {
  errorOnlyShellEventBus,
  verboseInfoShellEventBus,
} from "../universal/shell.ts";
import {
  executionPlanVisuals,
  ExecutionPlanVisualStyle,
} from "../universal/task-visuals.ts";
import {
  errorOnlyTaskEventBus,
  executionPlan,
  executionSubplan,
  Task,
  verboseInfoTaskEventBus,
} from "../universal/task.ts";
import { dedentIfFirstLineBlank } from "../universal/tmpl-literal-aide.ts";
import { computeSemVerSync } from "../universal/version.ts";
import { SidecarOpts, watcher, WatcherEvents } from "../universal/watcher.ts";
import { sqlPageConf } from "./conf.ts";
import {
  isSqlPageContent,
  normalizeSPC,
  SqlPageContent,
  SqlPageFilesUpsertDialect,
  sqlPageFilesUpsertDML,
} from "./content.ts";
import { SqlPagePlaybook, sqlPagePlaybookState } from "./playbook.ts";
import { isRouteSupplier } from "./route.ts";

// deno-lint-ignore no-explicit-any
type Any = any;

export type LsCommandRow = SqlPageContent & {
  name: string;
  notebook: string;
  flags: {
    isRouteSupplier: boolean;
    isPartialInjected: boolean;
    isAutoGenerated: boolean;
    isError: boolean;
    isInterpolated: boolean;
    isVirtual: boolean;
    isBinary: boolean;
  };
  error?: unknown;
};

export enum VerboseStyle {
  Plain = "plain",
  Rich = "rich",
  Markdown = "markdown",
}

export function informationalEventBuses<T extends Task, Context>(
  verbose?: VerboseStyle,
) {
  if (!verbose) {
    return {
      shellEventBus: errorOnlyShellEventBus({ style: "rich" }),
      tasksEventBus: errorOnlyTaskEventBus<T, Context>({ style: "rich" }),
    };
  }

  switch (verbose) {
    case VerboseStyle.Plain:
      return {
        shellEventBus: verboseInfoShellEventBus({ style: "plain" }),
        tasksEventBus: verboseInfoTaskEventBus<T, Context>({ style: "plain" }),
      };

    case VerboseStyle.Rich:
      return {
        shellEventBus: verboseInfoShellEventBus({ style: "rich" }),
        tasksEventBus: verboseInfoTaskEventBus<T, Context>({ style: "rich" }),
      };

    case VerboseStyle.Markdown: {
      const md = new MarkdownDoc();
      const mdSEB = markdownShellEventBus({ md });
      return {
        mdSEB,
        shellEventBus: mdSEB.bus,
        tasksEventBus: undefined, // TODO: add tasks to markdown
        md,
        emit: () => console.log(md.write()),
      };
    }
  }
}

const flagsFrom = (spc: SqlPageContent) => {
  switch (spc.kind) {
    case "head_sql":
    case "tail_sql":
      return {
        isAutoGenerated: false,
        isInterpolated: false,
        isError: false,
        isPartialInjected: false,
        isRouteSupplier: false,
        isVirtual: spc.cell?.isVirtual ?? false,
        isBinary: spc.cell?.sourceElaboration?.isRefToBinary ?? false,
      };

    case "sqlpage_file_upsert":
      return {
        isAutoGenerated: spc.isAutoGenerated ? true : false,
        isInterpolated: spc.isInterpolated ? true : false,
        isError: spc.error ? true : false,
        isPartialInjected: spc.partialInjected ? true : false,
        isRouteSupplier: isRouteSupplier(spc.cell?.attrs) ? true : false,
        isVirtual: spc.cell?.isVirtual ?? false,
        isBinary: spc.cell?.sourceElaboration?.isRefToBinary ?? false,
      };
  }
};

/**
 * Ensure all ancestor directories exist as rows.
 * - items: your existing rows (any shape)
 * - pathOf: how to extract a path string from a row
 * - makeRow: how to create a row for a missing directory, given its path
 * - isFile (optional): how to decide if a path is a file; defaults to "last segment contains a dot"
 */
export function upsertMissingAncestors<T>(
  items: T[],
  pathOf: (item: T) => string,
  makeRow: (dirPath: string) => T,
  isFile: (path: string) => boolean = (p) => {
    const segs = p.split("/").filter(Boolean);
    return segs.length > 0 && segs[segs.length - 1].includes(".");
  },
): T[] {
  const seen = new Set(items.map(pathOf));
  const out = [...items];

  for (const item of items) {
    const p = pathOf(item);
    const segs = p.split("/").filter(Boolean);
    const max = isFile(p) ? segs.length - 1 : segs.length;

    for (let i = 1; i <= max; i++) {
      const dirPath = segs.slice(0, i).join("/");
      if (!seen.has(dirPath)) {
        out.push(makeRow(dirPath));
        seen.add(dirPath);
      }
    }
  }
  return out;
}

// import { fromFileUrl, join, relative } from "jsr:@std/path";

export async function projectPaths(projectHome = Deno.cwd()) {
  const cliModuleUrl = new URL(import.meta.url);
  const isRemote = cliModuleUrl.protocol === "http:" ||
    cliModuleUrl.protocol === "https:";

  const absPathToSpryTsLocal = join(projectHome, "spry.ts");
  const absPathToSpryfileLocal = join(projectHome, "Spryfile.md");

  let importSpecifierForSpry: string;
  let importSpecifierForSpryLatest: string;

  if (isRemote) {
    const CANONICAL =
      "https://raw.githubusercontent.com/programmablemd/spry/refs/heads/main/lib/sqlpage/cli.ts";

    const headers: Record<string, string> = {
      "Accept": "application/vnd.github+json",
    };
    const token = Deno.env.get("GITHUB_TOKEN");
    if (token) headers.Authorization = `Bearer ${token}`;

    const latestTag = await (async () => {
      const r1 = await fetch(
        "https://api.github.com/repos/programmablemd/spry/releases/latest",
        { headers },
      );
      if (r1.ok) {
        const j = await r1.json();
        const t = (j?.tag_name ?? "").toString().trim();
        if (t) return t;
      }
      const r2 = await fetch(
        "https://api.github.com/repos/programmablemd/spry/tags?per_page=1",
        { headers },
      );
      if (r2.ok) {
        const a = await r2.json();
        const t = (a?.[0]?.name ?? "").toString().trim();
        if (t) return t;
      }
      throw new Error(`Unable to retrieve latest tag for ${CANONICAL}`); // fallback
    })();

    importSpecifierForSpry = CANONICAL;
    importSpecifierForSpryLatest = CANONICAL.replace(
      "/refs/heads/main/",
      `/refs/tags/${latestTag}/`,
    );
  } else {
    const cliFsPath = fromFileUrl(cliModuleUrl);
    let rel = relative(projectHome, cliFsPath).replaceAll("\\", "/");
    if (!rel.startsWith(".") && !rel.startsWith("/")) rel = `./${rel}`;
    importSpecifierForSpry = rel;
    importSpecifierForSpryLatest = rel;
  }

  return {
    projectHome,
    absPathToSpryTsLocal,
    absPathToSpryfileLocal,
    importSpecifierForSpry,
    importSpecifierForSpryLatest,
    isRemote,
    cliModuleUrl,
  };
}

export class CLI<Project> {
  constructor(
    readonly project: Project,
    readonly spn = SqlPagePlaybook.instance<Project>(project),
  ) {
  }

  // wrap this in
  async projectPaths(projectHome = Deno.cwd()) {
    return await projectPaths(projectHome);
  }

  async init(
    projectHome = Deno.cwd(),
    init?: {
      force?: boolean;
      dialect?: SqlPageFilesUpsertDialect;
    },
  ) {
    const {
      absPathToSpryTsLocal,
      absPathToSpryfileLocal,
      importSpecifierForSpryLatest,
    } = await this.projectPaths(projectHome);

    // spry.ts template (imports CLI from remote/local depending on our own location)
    const spryTs = (importSpec = importSpecifierForSpryLatest) =>
      dedentIfFirstLineBlank(`
      #!/usr/bin/env -S deno run -A --node-modules-dir=auto
      // Use \`deno run -A --watch\` in the shebang if you're contributing / developing Spry itself.

      import { CLI } from "${importSpec}";

      CLI.instance().run();`);

    const exists = async (path: string) =>
      await Deno.stat(path).catch(() => false);
    const relativeToCWD = (path: string) => relative(Deno.cwd(), path);

    const removed: string[] = [];
    if (init?.force) {
      if (await exists(absPathToSpryTsLocal)) {
        await Deno.remove(absPathToSpryTsLocal);
        removed.push(relativeToCWD(absPathToSpryTsLocal));
      }
    }

    const ignored: string[] = [];
    const created: string[] = [];

    if (!await exists(absPathToSpryTsLocal)) {
      await Deno.writeTextFile(absPathToSpryTsLocal, spryTs());
      created.push(relativeToCWD(absPathToSpryTsLocal));
      await Deno.chmod(absPathToSpryTsLocal, 0o755);
    } else {
      ignored.push(relativeToCWD(absPathToSpryTsLocal));
    }

    const webRoot = "dev-src.auto";
    if (!await exists(absPathToSpryfileLocal)) {
      const sfMD = new MarkdownDoc();
      const frontMatter = {
        "sqlpage-conf": {
          allow_exec: true,
          port: "${env.PORT}",
          database_url: "${env.SPRY_DB}",
          web_root: `./${webRoot}`,
          ...(init?.dialect === SqlPageFilesUpsertDialect.PostgreSQL
            ? { listen_on: "0.0.0.0:${env.PORT}" }
            : {}),
        },
      };
      sfMD.frontMatterOnceWithQuotes(frontMatter);
      sfMD.h1("Sample Spryfile.md");
      sfMD.title(2, "Environment variables and .envrc");
      sfMD.p(
        "Recommended practice is to keep these values in a local, directory-scoped environment file. If you use direnv (recommended), create a file named `.envrc` in this directory.",
      );
      sfMD.p("POSIX-style example (bash/zsh):");
      sfMD.codeTag(
        `envrc prepare-env -C ./.envrc --gitignore --descr "Generate .envrc file and add it to local .gitignore if it's not already there"`,
      )`${
        init?.dialect === SqlPageFilesUpsertDialect.SQLite
          ? `export DB_NAME="sqlpage.db"\n`
          : ``
      }export SPRY_DB=${
        init?.dialect === SqlPageFilesUpsertDialect.PostgreSQL
          ? `"postgresql://<username>:<password>@<host>:<port>/<database>"`
          : `"sqlite://$DB_NAME?mode=rwc"`
      }\nexport PORT=9227`;
      sfMD.p(
        "Then run `direnv allow` in this project directory to load the `.envrc` into your shell environment. direnv will evaluate `.envrc` only after you explicitly allow it.",
      );
      sfMD.title(2, "SQLPage Dev / Watch mode");
      sfMD.p(
        "While you're developing, Spry's `dev-src.auto` generator should be used:",
      );
      sfMD.codeTag(
        `bash prepare-sqlpage-dev --descr "Generate the dev-src.auto directory to work in ${init?.dialect} dev mode"`,
      )`./spry.ts spc --fs dev-src.auto --destroy-first --conf sqlpage/sqlpage.json`;
      sfMD.codeTag(
        `bash clean --descr "Clean up the project directory's generated artifacts"`,
      )`rm -rf dev-src.auto`;
      sfMD.p(
        "In development mode, here’s the `--watch` convenience you can use so that\nwhenever you update `Spryfile.md`, it regenerates the SQLPage `dev-src.auto`,\nwhich is then picked up automatically by the SQLPage server:",
      );
      sfMD.codeTag(
        `bash`,
      )`./spry.ts spc --fs dev-src.auto --destroy-first --conf sqlpage/sqlpage.json --watch --with-sqlpage`;
      sfMD.ul(
        "--watch` turns on watching all `--md` files passed in (defaults to `Spryfile.md`)",
      );
      sfMD.ul("--with-sqlpage` starts and stops SQLPage after each build");
      sfMD.p(
        "Restarting SQLPage after each re-generation of dev-src.auto is **not**\nnecessary, so you can also use `--watch` without `--with-sqlpage` in one\nterminal window while keeping the SQLPage server running in another terminal\nwindow.",
      );
      sfMD.p("If you're running SQLPage in another terminal window, use:");
      sfMD.codeTag(
        `bash`,
      )`./spry.ts spc --fs dev-src.auto --destroy-first --conf sqlpage/sqlpage.json --watch`;
      sfMD.title(2, "SQLPage single database deployment mode");
      sfMD.p(
        "After development is complete, the `dev-src.auto` can be removed and single-database deployment can be used:",
      );
      sfMD.codeTag(
        `bash deploy --descr "Generate sqlpage_files table upsert SQL and push them to ${init?.dialect}"`,
      )`rm -rf dev-src.auto\n./spry.ts spc --package ${
        init?.dialect ? `--dialect ${init?.dialect}` : ``
      } --conf sqlpage/sqlpage.json | ${
        init?.dialect === "postgres" ? `psql` : `sqlite3`
      } ${init?.dialect === "postgres" ? "$SPRY_DB" : "$DB_NAME"}`;
      sfMD.title(2, "Start the SQLPage server");
      sfMD.codeTag(
        `bash`,
      )`sqlpage`;

      sfMD.p("You can create fenced cells for `bash`, `sql`, etc. here.");
      sfMD.p("TODO: add examples with `doctor`, `prepare-db`, etc.");
      sfMD.codeTag(
        `sql index.sql { route: { caption: "Home" } }`,
      )`select 'card' as component,\n'Spry' as title,\n1 as columns;\nselect 'Use Markdown to Code, Build, and Orchestrate Intelligence'  as title,\n'https://sprymd.org' as link,\n'Spry turns Markdown into a programmable medium. Every fenced block, directive, and section executes, verifies, and composes reproducible workflows. From SQL pipelines to AI context graphs, Spry unifies your code, data, and documentation into one living system of record.' as description;`;
      await Deno.writeTextFile(absPathToSpryfileLocal, sfMD.write());
      created.push(relativeToCWD(absPathToSpryfileLocal));
    } else {
      ignored.push(relativeToCWD(absPathToSpryfileLocal));
    }

    return { removed, ignored, created, gitignore: await gitignore(webRoot) };
  }

  lsColorPathField<Row extends { notebook: string }>(
    header: string,
  ): Partial<ColumnDef<Row, string>> {
    return {
      header,
      format: (supplied) => {
        const p = relative(Deno.cwd(), supplied);
        const i = p.lastIndexOf("/");
        return i < 0 ? bold(p) : gray(p.slice(0, i + 1)) + bold(p.slice(i + 1));
      },
      rules: [{
        when: (_v, r) =>
          "error" in r
            ? ((r.error ? String(r.error)?.trim().length ?? 0 : 0) > 0)
            : false,
        color: red,
      }],
    };
  }

  lsNaturePathField<Row extends LsCommandRow>(): Partial<
    ColumnDef<Row, string>
  > {
    const lscpf = this.lsColorPathField("Path");
    return {
      ...lscpf,
      rules: [...(lscpf.rules ? lscpf.rules : []), {
        when: (_v, r) => r.kind === "sqlpage_file_upsert" && !r.isAutoGenerated,
        color: brightYellow,
      }],
    };
  }

  lsNatureField<Row extends LsCommandRow>(): Partial<
    ColumnDef<Row, Row["kind"]>
  > {
    return {
      header: "Nature",
      format: (v) =>
        v === "head_sql"
          ? green(v)
          : v === "tail_sql"
          ? yellow(v)
          : v === "sqlpage_file_upsert"
          ? brightYellow(v)
          : cyan(v),
    };
  }

  lsFlagsField<Row extends LsCommandRow>():
    | Partial<ColumnDef<Row, Row["flags"]>>
    | undefined {
    return {
      header: "Flags",
      defaultColor: gray,
      // deno-fmt-ignore
      format: (v) =>
        `${brightYellow(v.isInterpolated ? "I" : " ")} ${yellow(v.isRouteSupplier ? "R" : " ")} ${v.isAutoGenerated ? "A" : " "} ${v.isError ? "E" : " "} ${v.isPartialInjected ? "P" : " "} ${v.isVirtual ? "V" : " "} ${v.isBinary ? "B" : " "}`,
    };
  }

  async ls(
    opts: {
      md: string[];
      srcRelTo: SourceRelativeTo;
      conf?: boolean;
      pi?: boolean;
      infoAttrs?: boolean;
      tree?: boolean;
    },
  ) {
    const { items } = await collectAsyncGenerated(
      this.spn.sqlPageFiles({
        mdSources: opts.md,
        srcRelTo: opts.srcRelTo,
        state: sqlPagePlaybookState(),
      }),
    );
    let spfe = items.map((spf) => ({
      ...spf,
      name: basename(spf.path),
      flags: flagsFrom(spf),
      notebook: spf.cell?.provenance ?? "",
    }));

    if (opts.tree) {
      spfe = upsertMissingAncestors<LsCommandRow>(
        spfe.map((spf) => ({
          ...spf,
          path: relative(Deno.cwd(), spf.path),
        })),
        (r) => r.path,
        (path) => ({
          // deno-lint-ignore no-explicit-any
          kind: "" as any,
          path,
          contents: "virtual",
          asErrorContents: () => "virtual",
          name: basename(path),
          notebook: "",
          flags: {
            isAutoGenerated: false,
            isInterpolated: false,
            isError: false,
            isPartialInjected: false,
            isRouteSupplier: false,
            isVirtual: false,
            isBinary: false,
          },
        }),
      );

      const base = new ListerBuilder<LsCommandRow>()
        .declareColumns("kind", "name", "flags", "error", "notebook")
        .from(spfe)
        .field("name", "name", this.lsNaturePathField())
        .field("kind", "kind", this.lsNatureField())
        .field("flags", "flags", this.lsFlagsField())
        .field("error", "error", { header: "Err" })
        .field("notebook", "notebook", this.lsColorPathField("Notebook"))
        // IMPORTANT: make the tree column first so glyphs appear next to it
        .select("name", "kind", "flags", "error", "notebook");
      const tree = TreeLister
        .wrap(base)
        .from(spfe)
        .byPath({ pathKey: "path", separator: "/" })
        .treeOn("name");
      await tree.ls(true);
    } else if (opts.pi || opts.infoAttrs) {
      const pc = await this.spn.populateContent({
        mdSources: opts.md,
        srcRelTo: opts.srcRelTo,
        state: sqlPagePlaybookState(),
      });
      await new ListerBuilder<
        {
          line: number;
          language: string;
          pi: string;
          virtual: string;
          binary: string;
          notebook: string;
        }
      >()
        .declareColumns(
          "line",
          "language",
          "pi",
          "virtual",
          "binary",
          "notebook",
        )
        .from(
          pc.state.directives.tasks.map((cell) => ({
            line: cell.startLine ?? -1,
            language: cell.language ?? "?",
            pi: cell.pi ?? "?",
            virtual: cell.isVirtual ? "V" : " ",
            binary: cell.sourceElaboration?.isRefToBinary ? "B" : " ",
            notebook: cell.provenance ?? "",
          })),
        )
        .field("line", "line", { header: "L#" })
        .field("language", "language", { header: "Lang" })
        .field("virtual", "virtual", { header: "V" })
        .field("binary", "binary", { header: "B" })
        .field("pi", "pi", { header: "Cell PI" })
        .field("notebook", "notebook", this.lsColorPathField("Notebook"))
        .build()
        .ls(true);
    } else {
      await new ListerBuilder<LsCommandRow>()
        .declareColumns("kind", "path", "flags", "notebook", "error")
        .from(spfe)
        .field("kind", "kind", this.lsNatureField())
        .field("path", "path", this.lsNaturePathField())
        .field("flags", "flags", this.lsFlagsField())
        .field("error", "error", { header: "Err" })
        .field("notebook", "notebook", this.lsColorPathField("Notebook"))
        .sortBy("path").sortDir("asc")
        .build()
        .ls(true);
    }
  }

  async cat(
    opts: { md: string[]; srcRelTo: SourceRelativeTo; glob: string[] },
  ) {
    const matchesAnyGlob = (path: string) =>
      opts.glob.some((g) =>
        globToRegExp(g, { extended: true, globstar: true }).test(path)
      );

    const { items } = await collectAsyncGenerated(
      this.spn.sqlPageFiles({
        mdSources: opts.md,
        srcRelTo: opts.srcRelTo,
        state: sqlPagePlaybookState(),
      }),
    );

    for (const spf of items) {
      if (matchesAnyGlob(spf.path)) {
        console.log(spf.contents);
      }
    }
  }

  async *materializeFs(
    opts: {
      md: string[];
      srcRelTo: SourceRelativeTo;
      fs: string;
      destroyFirst?: boolean;
    },
  ) {
    const { fs } = opts;
    if (opts.destroyFirst) {
      try {
        await Deno.remove(fs, { recursive: true });
        console.info(`Removed ${fs}`);
      } catch (err) {
        if (!(err instanceof Deno.errors.NotFound)) {
          console.error(`Error while trying to remove ${fs}`, err);
        }
      }
    }
    for await (
      const spf of normalizeSPC(this.spn.sqlPageFiles({
        mdSources: opts.md,
        srcRelTo: opts.srcRelTo,
        state: sqlPagePlaybookState(),
      }))
    ) {
      const absPath = join(fs, spf.path);
      await ensureDir(dirname(absPath));
      if (typeof spf.contents === "string") {
        await Deno.writeTextFile(absPath, spf.contents);
      } else {
        await Deno.writeFile(absPath, spf.contents);
      }
      yield { ...spf, absPath };
    }
  }

  async materializeFsWatch(
    opts: {
      md: string[];
      srcRelTo: SourceRelativeTo;
      fs: string;
      destroyFirst?: boolean;
      watch?: boolean;
      verbose?: boolean;
      withSqlPage?: {
        enabled?: boolean;
        sitePrefix?: string;
        sqlPageBin?: string;
      };
    },
  ) {
    const sidecars = opts.withSqlPage?.enabled
      ? [
        {
          name: "sqlpage",
          cmd: [opts?.withSqlPage?.sqlPageBin ?? "sqlpage"],
          env: {
            SQLPAGE_SITE_PREFIX: opts.withSqlPage?.sitePrefix ?? "",
            ...Deno.env.toObject(),
          },
          shutdownSignal: "SIGTERM",
          shutdownTimeoutMs: 1500,
        } satisfies SidecarOpts,
      ]
      : undefined;

    const bus = eventBus<WatcherEvents>();
    const run = watcher(
      opts.md,
      async () => {
        for await (const spf of this.materializeFs(opts)) {
          if (opts.verbose) console.log(spf.path);
        }
      },
      {
        debounceMs: 120,
        recursive: false,
        bus,
        sidecars,
      },
    );

    // Example listeners (optional)
    bus.on(
      "run:begin",
      (ev) =>
        console.log(
          `[watch] build ${ev.runIndex} begin with SQLPage: ${
            opts.withSqlPage?.enabled ?? "no"
          }`,
        ),
    );
    bus.on("run:success", () => console.log("[watch] build success"));
    bus.on(
      "run:error",
      ({ error }) => console.error("[watch] build error:", error),
    );

    await run(opts.watch);
  }

  executableTasksFilter() {
    return (t: TaskCell<string>) =>
      t.taskDirective.nature === "TASK" ||
      (t.taskDirective.nature === "CONTENT" &&
        isSqlPageContent(t.taskDirective.content) == false);
  }

  command(name = "spry.ts") {
    const srcRelTo = new EnumType(SourceRelativeTo);
    const dialect = new EnumType(SqlPageFilesUpsertDialect);
    const verboseStyle = new EnumType(VerboseStyle);
    const mdOpt = [
      "-m, --md <mdPath:string>",
      "Use the given Markdown source(s), multiple allowed",
      {
        required: true,
        collect: true,
        default: ["Spryfile.md"],
      },
    ] as const;
    const srcRelToOpt = [
      "--src-rel-to <relative-to:sourceRelTo>",
      "When relative paths are used, what are they relative to?",
      {
        default: SourceRelativeTo.LocalFs,
      },
    ] as const;
    const verboseOpt = [
      "--verbose <style:verboseStyle>",
      "Emit information messages verbosely",
    ] as const;

    return new Command()
      .name(name)
      .type("dialect", dialect)
      .version(() => computeSemVerSync(import.meta.url))
      .description(
        "SQLPage Markdown Notebook: emit SQL package, write sqlpage.json, or materialize filesystem.",
      )
      .command("help", new HelpCommand().global())
      .command("completions", new CompletionsCommand())
      .command(
        "init",
        new Command()
          .description(
            "Setup Spryfile.md and spry.ts for local dev environment",
          )
          .type("dialect", dialect)
          /* .option("--db-name <file>", "name of SQLite database", {
            default: "sqlpage.db",
          }) */
          .option("--force", "Remove existing and recreate from latest tag", {
            default: false,
          })
          .option(
            "-d, --dialect <dialect:dialect>",
            "SQL dialect for package generation (sqlite or postgres)",
            { default: SqlPageFilesUpsertDialect.SQLite },
          )
          .action(async (opts) => {
            const { created, removed, ignored, gitignore: gi } = await this
              .init(
                Deno.cwd(),
                opts,
              );
            removed.forEach((r) => console.warn(`❌ Removed ${r}`));
            created.forEach((c) => console.info(`📄 Created ${c}`));
            ignored.forEach((i) => console.info(`🆗 Preserved ${i}`));

            const { added, preserved } = gi;
            added.forEach((c) => console.info(`📄 Added ${c} to .gitignore`));
            preserved.forEach((p) =>
              console.info(`🆗 Preserved ${p} in .gitignore`)
            );
          }),
      )
      .command("doctor", "Show dependencies and their availability")
      .action(async () => {
        const diags = doctor(["deno --version", "sqlpage --version"]);
        const result = await diags.run();
        diags.render.cli(result);
      })
      .command(
        "spc",
        new Command() // Emit SQL package (sqlite) to stdout; accepts md path
          .description("SQLPage Content (spc) CLI")
          .type("sourceRelTo", srcRelTo)
          .type("dialect", dialect)
          .option(...mdOpt)
          .option(...srcRelToOpt)
          .option(
            "-p, --package",
            "Emit SQL package to stdout from the given markdown path",
            { conflicts: ["fs"] },
          )
          .option(
            "-d, --dialect <dialect:dialect>",
            "SQL dialect for package generation (sqlite or postgres)",
            { default: SqlPageFilesUpsertDialect.SQLite },
          )
          // Materialize files to a target directory
          .option(
            "--fs <srcHome:string>",
            "Materialize SQL files under this directory.",
            { conflicts: ["package"], depends: ["conf"] },
          )
          .option(
            "--destroy-first",
            "Remove the directory that --fs points to before materializing SQL files",
            { depends: ["fs"] },
          )
          .option(
            "--watch",
            "Materialize SQL files under this directory every time the markdown sources change",
            { depends: ["fs"] },
          )
          .option(
            "--with-sqlpage",
            "Start a local SQLPage binary in dev mode pointing to the --fs directory",
            { depends: ["watch"] },
          )
          .option(
            "--sqlpage-bin <bin:string>",
            "Start a local SQLPage binary in dev mode pointing to the --fs directory",
            { depends: ["watch"], default: "sqlpage" },
          )
          // Write sqlpage.json to the given path
          .option(
            "-c, --conf <confPath:string>",
            "Write sqlpage.json to this path (generated from frontmatter sqlpage-conf).",
          )
          .option("--verbose", "Emit information messages")
          .action(async (opts) => {
            // If --fs is present, materialize files under that root
            if (typeof opts.fs === "string" && opts.fs.length > 0) {
              await this.materializeFsWatch({
                // not sure why this mapping is needed, Cliffy seems to not type `default` for `collect`ed arrays properly?
                md: opts.md.map((f) => String(f)),
                srcRelTo: opts.srcRelTo,
                fs: opts.fs,
                destroyFirst: opts.destroyFirst,
                verbose: opts.verbose,
                watch: opts?.watch,
                withSqlPage: {
                  enabled: opts.withSqlpage,
                  sqlPageBin: opts.sqlpageBin,
                },
              });
            }

            // If -p/--package is present (i.e., user requested SQL package), emit to stdout
            if (opts.package) {
              for (
                const chunk of await sqlPageFilesUpsertDML(
                  this.spn.sqlPageFiles({
                    mdSources: opts.md.map((f) => String(f)),
                    srcRelTo: opts.srcRelTo,
                    state: sqlPagePlaybookState(),
                  }),
                  {
                    dialect: opts.dialect
                      ? opts.dialect
                      : SqlPageFilesUpsertDialect.SQLite,
                    includeSqlPageFilesTable: true,
                  },
                )
              ) {
                console.log(chunk);
              }
            }

            // If --conf is present, write sqlpage.json
            if (opts.conf) {
              let emitted = 0, encountered = 0;
              const pp = await this.spn.populateContent({
                mdSources: opts.md.map((f) => String(f)),
                srcRelTo: opts.srcRelTo,
                state: sqlPagePlaybookState(),
              });
              for (const pb of pp.state.directives.playbooks) {
                encountered++;
                const { notebook: nb } = pb;
                if (nb.fm["sqlpage-conf"]) {
                  const json = sqlPageConf(nb.fm["sqlpage-conf"]);
                  // "web_root" should only be specified for `--fs`
                  // otherwise the directory won't exist
                  if (opts.package) delete json["web_root"];
                  await ensureDir(dirname(opts.conf));
                  await Deno.writeTextFile(
                    opts.conf,
                    JSON.stringify(json, null, 2),
                  );
                  if (opts.verbose) {
                    console.log(opts.conf);
                  }
                  emitted++;
                  break; // only pick from the first file
                }
              }
              if (emitted == 0) {
                console.warn(
                  `Encountered ${encountered} playbook(s) but no "sqlpage-conf" found in any frontmatter.`,
                );
              }
            }
          })
          .command("ls", "List SQLPage file entries")
          .type("sourceRelTo", srcRelTo)
          .option(...mdOpt)
          .option(...srcRelToOpt)
          .option(
            "-i, --pi",
            "Show just the cell names and INFO lines for each cell",
          )
          .option(
            "-I, --pi-attrs",
            "Show just the cell names and INFO and attributes for each cell",
          )
          .option("-t, --tree", "Show as tree")
          .action((opts) =>
            this.ls({
              ...opts,
              md: opts.md.map((f) => String(f)),
            })
          )
          .command("cat", "Concatenate SQLPage file contents")
          .type("sourceRelTo", srcRelTo)
          .option(...mdOpt)
          .option(...srcRelToOpt)
          .option("-g, --glob <path:string>", "Path glob(s) to target", {
            required: true,
            collect: true,
          })
          .action((opts) =>
            this.cat({
              ...opts,
              md: opts.md.map((f) => String(f)),
            })
          ),
      )
      .command(
        "task",
        new Command() // Emit SQL package (sqlite) to stdout; accepts md path
          .description(
            "Spry Task CLI (execute a specific cell and dependencies)",
          )
          .type("sourceRelTo", srcRelTo)
          .type("verboseStyle", verboseStyle)
          .arguments("<taskId>")
          .complete("taskId", async () => {
            const pp = await this.spn.populateContent({
              mdSources: ["Spryfile.md"],
              srcRelTo: SourceRelativeTo.LocalFs,
              state: sqlPagePlaybookState(),
            });
            return pp.state.directives.tasks.filter(
              this.executableTasksFilter(),
            ).map((t) => t.taskDirective.identity);
          })
          .option(...mdOpt)
          .option(...srcRelToOpt)
          .option(...verboseOpt)
          .option("--summarize", "Emit summary after execution in JSON")
          .action(async (opts, taskId) => {
            const pp = await this.spn.populateContent({
              mdSources: opts.md.map((f) => String(f)),
              srcRelTo: opts.srcRelTo,
              state: sqlPagePlaybookState(),
            });
            const tasks = pp.state.directives.tasks.filter(
              this.executableTasksFilter(),
            );
            if (tasks.find((t) => t.taskId() == taskId)) {
              const ieb = informationalEventBuses<
                TaskCell<string>,
                TaskExecContext
              >(opts?.verbose);
              const runbook = await executeTasks(
                executionSubplan(executionPlan(tasks), [taskId]),
                execTasksState(pp.state.directives, {
                  onCapture: gitignorableOnCapture,
                }),
                { shellBus: ieb.shellEventBus, tasksBus: ieb.tasksEventBus },
              );
              if (ieb.emit) ieb.emit();
              if (opts.summarize) {
                console.log(runbook);
              }
            } else {
              console.warn(`Task '${taskId}' not found.`);
            }
          })
          .command("ls", "List task cells")
          .type("sourceRelTo", srcRelTo)
          .option(...mdOpt)
          .option(...srcRelToOpt)
          .option(
            "-a, --all",
            "List all cells in addition to executables",
          )
          .option(
            "-s, --select <cql:string>",
            "Use Cell Query Language (CQL) to select cells to list",
          )
          .action(async (opts) => {
            const pp = await this.spn.populateContent({
              mdSources: opts.md.map((f) => String(f)),
              srcRelTo: opts.srcRelTo,
              state: sqlPagePlaybookState(),
            });
            if (opts.select) {
              const filterCQL = compileCqlMini<TaskCell<string>>(opts.select);
              taskCLI.ls(filterCQL(pp.state.directives.tasks));
            } else {
              taskCLI.ls(
                opts.all
                  ? pp.state.directives.tasks
                  : pp.state.directives.tasks.filter(
                    this.executableTasksFilter(),
                  ),
              );
            }
          }),
      ).command(
        "runbook",
        new Command() // Emit SQL package (sqlite) to stdout; accepts md path
          .description("Spry Runbook CLI (execute all cells in DAG order)")
          .type("sourceRelTo", srcRelTo)
          .type("verboseStyle", verboseStyle)
          .type("visualStyle", new EnumType(ExecutionPlanVisualStyle))
          .option(...mdOpt)
          .option(...srcRelToOpt)
          .option(...verboseOpt)
          .option("--summarize", "Emit summary after execution in JSON")
          .option(
            "-s, --select <cql:string>",
            "Use Cell Query Language (CQL) to select cells to run as part of runbook",
          )
          .option("--visualize <style:visualStyle>", "Visualize the DAG")
          .action(async (opts) => {
            const pp = await this.spn.populateContent({
              mdSources: opts.md.map((f) => String(f)),
              srcRelTo: opts.srcRelTo,
              state: sqlPagePlaybookState(),
            });
            let plan: ReturnType<typeof executionPlan>;
            if (opts.select) {
              const filterCQL = compileCqlMini<TaskCell<string>>(opts.select);
              plan = executionPlan(filterCQL(pp.state.directives.tasks));
            } else {
              plan = executionPlan(
                pp.state.directives.tasks.filter(this.executableTasksFilter()),
              );
            }
            if (opts?.visualize) {
              const epv = executionPlanVisuals(plan);
              console.log(epv.visualText(opts.visualize));
            } else {
              const ieb = informationalEventBuses<
                TaskCell<string>,
                TaskExecContext
              >(opts?.verbose);
              const runbook = await executeTasks(
                plan,
                execTasksState(pp.state.directives, {
                  onCapture: gitignorableOnCapture,
                }),
                { shellBus: ieb.shellEventBus, tasksBus: ieb.tasksEventBus },
              );
              if (ieb.emit) ieb.emit();
              if (opts.summarize) {
                console.log(runbook);
              }
            }
          })
          .command("ls", "List SQLPage file runbook entries")
          .type("sourceRelTo", srcRelTo)
          .option(...mdOpt)
          .option(...srcRelToOpt)
          .action(async (opts) => {
            const pp = await this.spn.populateContent({
              mdSources: opts.md.map((f) => String(f)),
              srcRelTo: opts.srcRelTo,
              state: sqlPagePlaybookState(),
            });
            taskCLI.ls(
              pp.state.directives.tasks.filter(this.executableTasksFilter()),
            );
          }),
      );
  }

  async run(argv: string[] = Deno.args, name = "spry.ts") {
    await this.command(name).parse(argv);
  }

  static instance<Project>(project: Project = {} as Project) {
    return new CLI<Project>(project);
  }
}

if (import.meta.main) {
  CLI.instance().run();
}
