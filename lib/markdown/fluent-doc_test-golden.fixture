---
title: Fluent MD Example
tags:
  - deno
  - md
draft: false
version: 1
---

<a id="intro"></a>
## Intro

Small description.

<a id="getting-started"></a>
## Getting Started

```bash
deno run -A main.ts
```

- Install Deno
- Run the script

<a id="background"></a>
## Background

<a id="findings"></a>
## Findings

Key results.

<a id="appendix"></a>
## Appendix

<a id="a"></a>
## A

<a id="b"></a>
### B

<a id="c"></a>
#### C

- [A](#a)
  - [B](#b)

<a id="table-demo"></a>
## Table Demo

| H1     |   H2    |    H3 |
| :----- | :-----: | ----: |
| a      | b<br>b2 |     c |
| longer |         | right |

<a id="code-demo"></a>
## Code Demo

````ts
const a = `tick`;
const b = '```';
````

````bash
echo "hello"
cat <<'EOF'
inside
```
EOF
````

<a id="links-media"></a>
## Links & Media

[OpenAI](https://openai.com)
![Alt](https://example.org/img.png)
> quoted
> text

- [x] Done
- [ ] Todo
