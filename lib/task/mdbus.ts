import { MarkdownDoc } from "../markdown/fluent-doc.ts";
import { eventBus } from "../universal/event-bus.ts";
import { ShellBusEvents } from "../universal/shell.ts";

/**
 * markdownShellEventBus
 *
 * A ShellBusEvents listener that appends a rich, human-readable,
 * audit-quality Markdown log into a provided MarkdownDoc.
 *
 * - Always uses rich / emoji formatting.
 * - Does NOT create its own MarkdownDoc — you hand one in so multiple subsystems
 *   (shell, fleetfolio scanners, etc.) can share a single long-lived log.
 * - Appends sections as events fire instead of streaming to console.
 *
 * Typical usage:
 *
 * ```ts
 * const md = new MarkdownDoc({
 *   anchors: true,
 *   frontMatter: {
 *     session: "daily infra sweep",
 *     purpose: "EAA evidence capture",
 *   },
 * });
 *
 * const { bus, write } = markdownShellEventBus({ md });
 *
 * const sh = shell({ bus });
 * await sh.spawnText("deno --version");
 *
 * console.log(write());
 * ```
 */
export function markdownShellEventBus(init: {
  /**
   * The running Markdown log document that we will enrich.
   * Caller owns it; we just keep appending.
   */
  md: MarkdownDoc;
}) {
  const { md } = init;

  // emojis are always ON for this event bus
  const E = {
    rocket: "🚀",
    check: "✅",
    cross: "❌",
    boom: "💥",
    play: "▶️",
    gear: "⚙️",
    page: "📄",
    broom: "🧹",
    timer: "⏱️",
  } as const;

  const em = {
    start: (s: string) => `${E.rocket} ${s}`,
    done: (s: string, ok: boolean) => `${ok ? E.check : E.cross} ${s}`,
    error: (s: string) => `${E.boom} ${s}`,
    play: (s: string) => `${E.play} ${s}`,
    gear: (s: string) => `${E.gear} ${s}`,
    page: (s: string) => `${E.page} ${s}`,
    broom: (s: string) => `${E.broom} ${s}`,
    timer: (ms?: number) =>
      ms === undefined ? "" : ` ${E.timer} ${Math.round(ms)}ms`,
  };

  // inline md helpers (we don't rely on ANSI color here because Markdown is the log)
  const mdFmt = {
    codeInline: (s: string) => "`" + s.replace(/`/g, "\\`") + "`",
    path: (s: string) => "`" + s.replace(/`/g, "\\`") + "`",
    bold: (s: string) => `**${s}**`,
  };

  const fmtArgs = (args: readonly string[]) =>
    args.map((a) => (/\s/.test(a) ? JSON.stringify(a) : a)).join(" ");

  const te = new TextDecoder();

  // ensure we have some top-level "Shell Session" section once.
  // We will not overwrite if caller already created one.
  md.section(
    "Shell Session",
    (m) => {
      m.p(
        "This section is generated by `markdownShellEventBus`. " +
          "It captures each shell task, command invocation, stdout/stderr, and outcomes " +
          "in Markdown form suitable for humans, audits, and long-term evidence logs.",
      );
      m.table(
        ["Field", "Value"],
        [
          ["Session started", new Date().toISOString()],
          ["Emitter", "markdownShellEventBus"],
        ],
        ["left", "left"],
      );
    },
    2,
    { id: "shell-session" },
  );

  // counters + helpers to group related lifecycle events into stable sub-sections
  let spawnSeq = 0;
  let currentSpawnIdx = 0;

  let taskSeq = 0;
  const taskTitleByIndex = new Map<number, string>();

  function spawnSectionTitle(idx: number, cmd: string, args: string[]) {
    // example: "🚀 Spawn 1. deno run main.ts --foo bar"
    const base = `${idx}. ${cmd} ${fmtArgs(args)}`.trim();
    return `${E.rocket} Spawn ${base}`;
  }

  function taskSectionTitle(idx: number /* line index */) {
    // example: "▶️ Task Line L3"
    return `${E.play} Task Line L${idx}`;
  }

  // The bus we return for shell() to emit into.
  const bus = eventBus<ShellBusEvents>();

  //
  // spawn:* lifecycle
  //
  bus.on("spawn:start", ({ cmd, args, cwd, hasStdin }) => {
    currentSpawnIdx = ++spawnSeq;
    const title = spawnSectionTitle(currentSpawnIdx, cmd, args);

    md.section(
      title,
      (m) => {
        m.p(em.start(mdFmt.bold("Command started")));
        m.table(
          ["Property", "Value"],
          [
            ["Command", mdFmt.codeInline(cmd)],
            ["Args", args.length ? fmtArgs(args) : "(none)"],
            ["CWD", cwd ? mdFmt.path(cwd) : "(inherit)"],
            ["stdin", hasStdin ? "piped" : "null"],
            ["Started at", new Date().toISOString()],
          ],
          ["left", "left"],
        );

        m.h3("Command Line");
        m.code(
          "bash",
          `${cmd} ${fmtArgs(args)}`.trim(),
        );
      },
      2,
      { id: `spawn-${currentSpawnIdx}` },
    );
  });

  bus.on(
    "spawn:done",
    ({ cmd, args, code, success, stdout, stderr, durationMs }) => {
      // append more info to the same spawn section
      const title = spawnSectionTitle(currentSpawnIdx, cmd, args);

      md.section(title, (m) => {
        m.h3(em.done("Result", success));

        m.table(
          ["Metric", "Value"],
          [
            ["Exit code", String(code)],
            ["Success", success ? "true" : "false"],
            [
              "Duration",
              `${Math.round(durationMs)}ms${em.timer(durationMs)}`,
            ],
          ],
          ["left", "left"],
        );

        if (stdout.length > 0) {
          m.h4("Captured stdout");
          m.code(
            "text",
            te.decode(stdout),
          );
        }

        if (stderr.length > 0) {
          m.h4("Captured stderr");
          m.code(
            "text",
            te.decode(stderr),
          );
        }
      });
    },
  );

  bus.on("spawn:error", ({ cmd, args, error }) => {
    // spawn:error might fire standalone, so give it its own seq
    const idx = ++spawnSeq;
    const title = spawnSectionTitle(idx, cmd, args);

    md.section(
      title,
      (m) => {
        m.h3(em.error("Spawn Error"));

        const errMsg = (error instanceof Error) ? error.message : String(error);

        m.table(
          ["Property", "Value"],
          [
            ["Command", mdFmt.codeInline(cmd)],
            ["Args", args.length ? fmtArgs(args) : "(none)"],
            ["Error", errMsg],
            ["When", new Date().toISOString()],
          ],
          ["left", "left"],
        );
      },
      2,
      { id: `spawn-${idx}` },
    );
  });

  //
  // task:line:* lifecycle for shell.auto() or shell.task()
  //
  bus.on("task:line:start", ({ index, line }) => {
    // Create the line section the first time we see this index.
    if (!taskTitleByIndex.has(index)) {
      taskSeq++;
      const title = taskSectionTitle(index);
      taskTitleByIndex.set(index, title);

      md.section(
        title,
        (m) => {
          m.p(em.play(mdFmt.bold("Task line started")));
          m.table(
            ["Property", "Value"],
            [
              ["Line index", `L${index}`],
              ["Started at", new Date().toISOString()],
            ],
            ["left", "left"],
          );

          m.h3("Line Content");
          m.code("bash", line);
        },
        2,
        { id: `task-${index}` },
      );
    } else {
      // re-open existing block if same logical line index is re-run
      const title = taskTitleByIndex.get(index)!;
      md.section(title, (m) => {
        m.p(em.play(mdFmt.bold("Task line re-run start")));
        m.table(
          ["Property", "Value"],
          [
            ["Line index", `L${index}`],
            ["Re-run at", new Date().toISOString()],
          ],
          ["left", "left"],
        );
      });
    }
  });

  bus.on(
    "task:line:done",
    ({ index, code, success, stdout, stderr, durationMs }) => {
      const title = taskTitleByIndex.get(index) ?? taskSectionTitle(index);

      md.section(title, (m) => {
        m.h3(em.done("Result", success));

        m.table(
          ["Metric", "Value"],
          [
            ["Exit code", String(code)],
            ["Success", success ? "true" : "false"],
            [
              "Duration",
              `${Math.round(durationMs)}ms${em.timer(durationMs)}`,
            ],
          ],
          ["left", "left"],
        );

        if (stdout.length > 0) {
          m.h4("Captured stdout");
          m.code(
            "text",
            te.decode(stdout),
          );
        }

        if (stderr.length > 0) {
          m.h4("Captured stderr");
          m.code(
            "text",
            te.decode(stderr),
          );
        }
      });
    },
  );

  //
  // shebang:* lifecycle for inline script tempfiles
  //
  bus.on("shebang:tempfile", ({ path }) => {
    const title = `${E.page} Shebang Script`;

    md.section(
      title,
      (m) => {
        m.h3(em.page("Temporary File Created"));

        m.table(
          ["Property", "Value"],
          [
            ["Temp file path", mdFmt.path(path)],
            ["Created at", new Date().toISOString()],
          ],
          ["left", "left"],
        );

        m.p(
          "The shell wrote inline script content to a temp file so it " +
            "could be executed via its declared `#!` interpreter.",
        );
      },
      2,
      { id: "shebang" },
    );
  });

  bus.on("shebang:cleanup", ({ path, ok, error }) => {
    const title = `${E.page} Shebang Script`;

    md.section(title, (m) => {
      m.h3(em.broom("Cleanup"));

      const errMsg = ok ? "" : (error ? String(error) : "error");

      m.table(
        ["Property", "Value"],
        [
          ["Temp file path", mdFmt.path(path)],
          ["Cleanup status", ok ? "ok" : "error"],
          ["Cleanup error", errMsg || "(none)"],
          ["When", new Date().toISOString()],
        ],
        ["left", "left"],
      );

      if (!ok) {
        m.quote(
          "Cleanup failed. Manual removal of the temp file may be required.",
        );
      }
    });
  });

  //
  // auto:mode lifecycle (how shell.auto() decided to execute)
  //
  bus.on("auto:mode", ({ mode }) => {
    const title = `${E.gear} Automation Mode`;

    md.section(
      title,
      (m) => {
        m.p(
          em.gear(
            mdFmt.bold("Inline task execution mode selected"),
          ),
        );
        m.table(
          ["Property", "Value"],
          [
            [
              "Mode",
              mode === "shebang"
                ? "shebang (write temp file and exec interpreter)"
                : "eval (pipe lines directly into interpreter)",
            ],
            ["When", new Date().toISOString()],
          ],
          ["left", "left"],
        );
      },
      2,
      { id: "auto-mode" },
    );
  });

  // Caller can grab the md they already passed in,
  // but we also return a `write()` convenience for finalization.
  return {
    bus,
    write: () => md.write(),
  };
}
