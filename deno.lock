{"version": "5", "specifiers": {"jsr:@cliffy/command@1.0.0-rc.8": "1.0.0-rc.8", "jsr:@cliffy/flags@1.0.0-rc.8": "1.0.0-rc.8", "jsr:@cliffy/internal@1.0.0-rc.8": "1.0.0-rc.8", "jsr:@cliffy/table@1.0.0-rc.8": "1.0.0-rc.8", "jsr:@std/assert@1": "1.0.14", "jsr:@std/assert@1.0.7": "1.0.7", "jsr:@std/async@1": "1.0.15", "jsr:@std/encoding@1": "1.0.10", "jsr:@std/fmt@1": "1.0.8", "jsr:@std/fmt@~1.0.2": "1.0.8", "jsr:@std/fs@1": "1.0.19", "jsr:@std/internal@^1.0.10": "1.0.10", "jsr:@std/internal@^1.0.5": "1.0.10", "jsr:@std/internal@^1.0.9": "1.0.10", "jsr:@std/path@1": "1.1.2", "jsr:@std/path@^1.1.1": "1.1.2", "jsr:@std/text@~1.0.7": "1.0.16", "jsr:@std/yaml@1": "1.0.9", "jsr:@zod/zod@4": "4.1.12", "npm:@types/mdast@4": "4.0.4", "npm:json5@2": "2.2.3", "npm:mdast-util-to-string@4": "4.0.0", "npm:remark-frontmatter@5": "5.0.0", "npm:remark-gfm@4": "4.0.1", "npm:remark-stringify@11": "11.0.0", "npm:remark@15": "15.0.1"}, "jsr": {"@cliffy/command@1.0.0-rc.8": {"integrity": "758147790797c74a707e5294cc7285df665422a13d2a483437092ffce40b5557", "dependencies": ["jsr:@cliffy/flags", "jsr:@cliffy/internal", "jsr:@cliffy/table", "jsr:@std/fmt@~1.0.2", "jsr:@std/text"]}, "@cliffy/flags@1.0.0-rc.8": {"integrity": "0f1043ce6ef037ba1cb5fe6b1bcecb25dc2f29371a1c17f278ab0f45e4b6f46c", "dependencies": ["jsr:@std/text"]}, "@cliffy/internal@1.0.0-rc.8": {"integrity": "34cdf2fad9b084b5aed493b138d573f52d4e988767215f7460daf0b918ff43d8"}, "@cliffy/table@1.0.0-rc.8": {"integrity": "8bbcdc2ba5e0061b4b13810a24e6f5c6ab19c09f0cce9eb691ccd76c7c6c9db5", "dependencies": ["jsr:@std/fmt@~1.0.2"]}, "@std/assert@1.0.7": {"integrity": "64ce9fac879e0b9f3042a89b3c3f8ccfc9c984391af19e2087513a79d73e28c3", "dependencies": ["jsr:@std/internal@^1.0.5"]}, "@std/assert@1.0.14": {"integrity": "68d0d4a43b365abc927f45a9b85c639ea18a9fab96ad92281e493e4ed84abaa4", "dependencies": ["jsr:@std/internal@^1.0.10"]}, "@std/async@1.0.15": {"integrity": "55d1d9d04f99403fe5730ab16bdcc3c47f658a6bf054cafb38a50f046238116e"}, "@std/encoding@1.0.10": {"integrity": "8783c6384a2d13abd5e9e87a7ae0520a30e9f56aeeaa3bdf910a3eaaf5c811a1"}, "@std/fmt@1.0.8": {"integrity": "71e1fc498787e4434d213647a6e43e794af4fd393ef8f52062246e06f7e372b7"}, "@std/fs@1.0.19": {"integrity": "051968c2b1eae4d2ea9f79a08a3845740ef6af10356aff43d3e2ef11ed09fb06", "dependencies": ["jsr:@std/internal@^1.0.9", "jsr:@std/path@^1.1.1"]}, "@std/internal@1.0.10": {"integrity": "e3be62ce42cab0e177c27698e5d9800122f67b766a0bea6ca4867886cbde8cf7"}, "@std/path@1.1.2": {"integrity": "c0b13b97dfe06546d5e16bf3966b1cadf92e1cc83e56ba5476ad8b498d9e3038", "dependencies": ["jsr:@std/internal@^1.0.10"]}, "@std/text@1.0.16": {"integrity": "ddb9853b75119a2473857d691cf1ec02ad90793a2e8b4a4ac49d7354281a0cf8"}, "@std/yaml@1.0.9": {"integrity": "6bad3dc766dd85b4b37eabcba81b6aa4eac7a392792ae29abcfb0f90602d55bb"}, "@zod/zod@4.1.12": {"integrity": "5876ed4c6d44673faf5120f0a461a2ada2eb6c735329d3ebaf5ba1fc08387695"}}, "npm": {"@types/debug@4.1.12": {"integrity": "sha512-vIChWdVG3LG1SMxEvI/AK+FWJthlrqlTu7fbrlywTkkaONwk/UAGaULXRlf8vkzFBLVm0zkMdCquhL5aOjhXPQ==", "dependencies": ["@types/ms"]}, "@types/mdast@4.0.4": {"integrity": "sha512-kGaNbPh1k7AFzgpud/gMdvIm5xuECykRR+JnWKQno9TAXVa6WIVCGTPvYGekIDL4uwCZQSYbUxNBSb1aUo79oA==", "dependencies": ["@types/unist"]}, "@types/ms@2.1.0": {"integrity": "sha512-GsCCIZDE/p3i96vtEqx+7dBUGXrc7zeSK3wwPHIaRThS+9OhWIXRqzs4d6k1SVU8g91DrNRWxWUGhp5KXQb2VA=="}, "@types/unist@3.0.3": {"integrity": "sha512-ko/gIFJRv177XgZsZcBwnqJN5x/Gien8qNOn0D5bQU/zAzVf9Zt3BlcUiLqhV9y4ARk0GbT3tnUiPNgnTXzc/Q=="}, "bail@2.0.2": {"integrity": "sha512-0xO6mYd7JB2YesxDKplafRpsiOzPt9V02ddPCLbY1xYGPOX24NTyN50qnUxgCPcSoYMhKpAuBTjQoRZCAkUDRw=="}, "ccount@2.0.1": {"integrity": "sha512-eyrF0jiFpY+3drT6383f1qhkbGsLSifNAjA61IUjZjmLCWjItY6LB9ft9YhoDgwfmclB2zhu51Lc7+95b8NRAg=="}, "character-entities@2.0.2": {"integrity": "sha512-shx7oQ0Awen/BRIdkjkvz54PnEEI/EjwXDSIZp86/KKdbafHh1Df/RYGBhn4hbe2+uKC9FnT5UCEdyPz3ai9hQ=="}, "debug@4.4.1": {"integrity": "sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==", "dependencies": ["ms"]}, "decode-named-character-reference@1.2.0": {"integrity": "sha512-c6fcElNV6ShtZXmsgNgFFV5tVX2PaV4g+MOAkb8eXHvn6sryJBrZa9r0zV6+dtTyoCKxtDy5tyQ5ZwQuidtd+Q==", "dependencies": ["character-entities"]}, "dequal@2.0.3": {"integrity": "sha512-0je+qPKHEMohvfRTCEo3CrPG6cAzAYgmzKyxRiYSSDkS6eGJdyVJm7WaYA5ECaAD9wLB2T4EEeymA5aFVcYXCA=="}, "devlop@1.1.0": {"integrity": "sha512-RWmIqhcFf1lRYBvNmr7qTNuyCt/7/ns2jbpp1+PalgE/rDQcBT0fioSMUpJ93irlUhC5hrg4cYqe6U+0ImW0rA==", "dependencies": ["dequal"]}, "escape-string-regexp@5.0.0": {"integrity": "sha512-/veY75JbMK4j1yjvuUxuVsiS/hr/4iHs9FTT6cgTexxdE0Ly/glccBAkloH/DofkjRbZU3bnoj38mOmhkZ0lHw=="}, "extend@3.0.2": {"integrity": "sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g=="}, "fault@2.0.1": {"integrity": "sha512-WtySTkS4OKev5JtpHXnib4Gxiurzh5NCGvWrFaZ34m6JehfTUhKZvn9njTfw48t6JumVQOmrKqpmGcdwxnhqBQ==", "dependencies": ["format"]}, "format@0.2.2": {"integrity": "sha512-wzsgA6WOq+09wrU1tsJ09udeR/YZRaeArL9e1wPbFg3GG2yDnC2ldKpxs4xunpFF9DgqCqOIra3bc1HWrJ37Ww=="}, "is-plain-obj@4.1.0": {"integrity": "sha512-+Pgi+vMuUNkJyExiMBt5IlFoMyKnr5zhJ4Uspz58WOhBF5QoIZkFyNHIbBAtHwzVAgk5RtndVNsDRN61/mmDqg=="}, "json5@2.2.3": {"integrity": "sha512-<PERSON>m<PERSON>e7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==", "bin": true}, "longest-streak@3.1.0": {"integrity": "sha512-9Ri+o0JYgehTaVBBDoMqIl8GXtbWg711O3srftcHhZ0dqnETqLaoIK0x17fUw9rFSlK/0NlsKe0Ahhyl5pXE2g=="}, "markdown-table@3.0.4": {"integrity": "sha512-wiYz4+JrLyb/DqW2hkFJxP7Vd7JuTDm77fvbM8VfEQdmSMqcImWeeRbHwZjBjIFki/VaMK2BhFi7oUUZeM5bqw=="}, "mdast-util-find-and-replace@3.0.2": {"integrity": "sha512-Tmd1Vg/m3Xz43afeNxDIhWRtFZgM2VLyaf4vSTYwudTyeuTneoL3qtWMA5jeLyz/O1vDJmmV4QuScFCA2tBPwg==", "dependencies": ["@types/mdast", "escape-string-regexp", "unist-util-is", "unist-util-visit-parents"]}, "mdast-util-from-markdown@2.0.2": {"integrity": "sha512-uZhTV/8NBuw0WHkPTrCqDOl0zVe1BIng5ZtHoDk49ME1qqcjYmmLmOf0gELgcRMxN4w2iuIeVso5/6QymSrgmA==", "dependencies": ["@types/mdast", "@types/unist", "decode-named-character-reference", "<PERSON>v<PERSON>", "mdast-util-to-string", "micromark", "micromark-util-decode-numeric-character-reference", "micromark-util-decode-string", "micromark-util-normalize-identifier", "micromark-util-symbol", "micromark-util-types", "unist-util-stringify-position"]}, "mdast-util-frontmatter@2.0.1": {"integrity": "sha512-LRqI9+wdgC25P0URIJY9vwocIzCcksduHQ9OF2joxQoyTNVduwLAFUzjoopuRJbJAReaKrNQKAZKL3uCMugWJA==", "dependencies": ["@types/mdast", "<PERSON>v<PERSON>", "escape-string-regexp", "mdast-util-from-markdown", "mdast-util-to-markdown", "micromark-extension-frontmatter"]}, "mdast-util-gfm-autolink-literal@2.0.1": {"integrity": "sha512-5HVP2MKaP6L+G6YaxPNjuL0BPrq9orG3TsrZ9YXbA3vDw/ACI4MEsnoDpn6ZNm7GnZgtAcONJyPhOP8tNJQavQ==", "dependencies": ["@types/mdast", "ccount", "<PERSON>v<PERSON>", "mdast-util-find-and-replace", "micromark-util-character"]}, "mdast-util-gfm-footnote@2.1.0": {"integrity": "sha512-sqpDWlsHn7Ac9GNZQMeUzPQSMzR6Wv0WKRNvQRg0KqHh02fpTz69Qc1QSseNX29bhz1ROIyNyxExfawVKTm1GQ==", "dependencies": ["@types/mdast", "<PERSON>v<PERSON>", "mdast-util-from-markdown", "mdast-util-to-markdown", "micromark-util-normalize-identifier"]}, "mdast-util-gfm-strikethrough@2.0.0": {"integrity": "sha512-mKKb915TF+OC5ptj5bJ7WFRPdYtuHv0yTRxK2tJvi+BDqbkiG7h7u/9SI89nRAYcmap2xHQL9D+QG/6wSrTtXg==", "dependencies": ["@types/mdast", "mdast-util-from-markdown", "mdast-util-to-markdown"]}, "mdast-util-gfm-table@2.0.0": {"integrity": "sha512-78UEvebzz/rJIxLvE7ZtDd/vIQ0RHv+3Mh5DR96p7cS7HsBhYIICDBCu8csTNWNO6tBWfqXPWekRuj2FNOGOZg==", "dependencies": ["@types/mdast", "<PERSON>v<PERSON>", "markdown-table", "mdast-util-from-markdown", "mdast-util-to-markdown"]}, "mdast-util-gfm-task-list-item@2.0.0": {"integrity": "sha512-IrtvNvjxC1o06taBAVJznEnkiHxLFTzgonUdy8hzFVeDun0uTjxxrRGVaNFqkU1wJR3RBPEfsxmU6jDWPofrTQ==", "dependencies": ["@types/mdast", "<PERSON>v<PERSON>", "mdast-util-from-markdown", "mdast-util-to-markdown"]}, "mdast-util-gfm@3.1.0": {"integrity": "sha512-0ulfdQOM3ysHhCJ1p06l0b0VKlhU0wuQs3thxZQagjcjPrlFRqY215uZGHHJan9GEAXd9MbfPjFJz+qMkVR6zQ==", "dependencies": ["mdast-util-from-markdown", "mdast-util-gfm-autolink-literal", "mdast-util-gfm-footnote", "mdast-util-gfm-strikethrough", "mdast-util-gfm-table", "mdast-util-gfm-task-list-item", "mdast-util-to-markdown"]}, "mdast-util-phrasing@4.1.0": {"integrity": "sha512-TqICwyvJJpBwvGAMZjj4J2n0X8QWp21b9l0o7eXyVJ25YNWYbJDVIyD1bZXE6WtV6RmKJVYmQAKWa0zWOABz2w==", "dependencies": ["@types/mdast", "unist-util-is"]}, "mdast-util-to-markdown@2.1.2": {"integrity": "sha512-xj68wMTvGXVOKonmog6LwyJKrYXZPvlwabaryTjLh9LuvovB/KAH+kvi8Gjj+7rJjsFi23nkUxRQv1KqSroMqA==", "dependencies": ["@types/mdast", "@types/unist", "longest-streak", "mdast-util-phrasing", "mdast-util-to-string", "micromark-util-classify-character", "micromark-util-decode-string", "unist-util-visit", "zwitch"]}, "mdast-util-to-string@4.0.0": {"integrity": "sha512-0H44vDimn51F0YwvxSJSm0eCDOJTRlmN0R1yBh4HLj9wiV1Dn0QoXGbvFAWj2hSItVTlCmBF1hqKlIyUBVFLPg==", "dependencies": ["@types/mdast"]}, "micromark-core-commonmark@2.0.3": {"integrity": "sha512-RDBrHEMSxVFLg6xvnXmb1Ayr2WzLAWjeSATAoxwKYJV94TeNavgoIdA0a9ytzDSVzBy2YKFK+emCPOEibLeCrg==", "dependencies": ["decode-named-character-reference", "<PERSON>v<PERSON>", "micromark-factory-destination", "micromark-factory-label", "micromark-factory-space", "micromark-factory-title", "micromark-factory-whitespace", "micromark-util-character", "micromark-util-chunked", "micromark-util-classify-character", "micromark-util-html-tag-name", "micromark-util-normalize-identifier", "micromark-util-resolve-all", "micromark-util-subtokenize", "micromark-util-symbol", "micromark-util-types"]}, "micromark-extension-frontmatter@2.0.0": {"integrity": "sha512-C4AkuM3dA58cgZha7zVnuVxBhDsbttIMiytjgsM2XbHAB2faRVaHRle40558FBN+DJcrLNCoqG5mlrpdU4cRtg==", "dependencies": ["fault", "micromark-util-character", "micromark-util-symbol", "micromark-util-types"]}, "micromark-extension-gfm-autolink-literal@2.1.0": {"integrity": "sha512-oOg7knzhicgQ3t4QCjCWgTmfNhvQbDDnJeVu9v81r7NltNCVmhPy1fJRX27pISafdjL+SVc4d3l48Gb6pbRypw==", "dependencies": ["micromark-util-character", "micromark-util-sanitize-uri", "micromark-util-symbol", "micromark-util-types"]}, "micromark-extension-gfm-footnote@2.1.0": {"integrity": "sha512-/yPhxI1ntnDNsiHtzLKYnE3vf9JZ6cAisqVDauhp4CEHxlb4uoOTxOCJ+9s51bIB8U1N1FJ1RXOKTIlD5B/gqw==", "dependencies": ["<PERSON>v<PERSON>", "micromark-core-commonmark", "micromark-factory-space", "micromark-util-character", "micromark-util-normalize-identifier", "micromark-util-sanitize-uri", "micromark-util-symbol", "micromark-util-types"]}, "micromark-extension-gfm-strikethrough@2.1.0": {"integrity": "sha512-ADVjpOOkjz1hhkZLlBiYA9cR2Anf8F4HqZUO6e5eDcPQd0Txw5fxLzzxnEkSkfnD0wziSGiv7sYhk/ktvbf1uw==", "dependencies": ["<PERSON>v<PERSON>", "micromark-util-chunked", "micromark-util-classify-character", "micromark-util-resolve-all", "micromark-util-symbol", "micromark-util-types"]}, "micromark-extension-gfm-table@2.1.1": {"integrity": "sha512-t2OU/dXXioARrC6yWfJ4hqB7rct14e8f7m0cbI5hUmDyyIlwv5vEtooptH8INkbLzOatzKuVbQmAYcbWoyz6Dg==", "dependencies": ["<PERSON>v<PERSON>", "micromark-factory-space", "micromark-util-character", "micromark-util-symbol", "micromark-util-types"]}, "micromark-extension-gfm-tagfilter@2.0.0": {"integrity": "sha512-xHlTOmuCSotIA8TW1mDIM6X2O1SiX5P9IuDtqGonFhEK0qgRI4yeC6vMxEV2dgyr2TiD+2PQ10o+cOhdVAcwfg==", "dependencies": ["micromark-util-types"]}, "micromark-extension-gfm-task-list-item@2.1.0": {"integrity": "sha512-qIBZhqxqI6fjLDYFTBIa4eivDMnP+OZqsNwmQ3xNLE4Cxwc+zfQEfbs6tzAo2Hjq+bh6q5F+Z8/cksrLFYWQQw==", "dependencies": ["<PERSON>v<PERSON>", "micromark-factory-space", "micromark-util-character", "micromark-util-symbol", "micromark-util-types"]}, "micromark-extension-gfm@3.0.0": {"integrity": "sha512-vsKArQsicm7t0z2GugkCKtZehqUm31oeGBV/KVSorWSy8ZlNAv7ytjFhvaryUiCUJYqs+NoE6AFhpQvBTM6Q4w==", "dependencies": ["micromark-extension-gfm-autolink-literal", "micromark-extension-gfm-footnote", "micromark-extension-gfm-strikethrough", "micromark-extension-gfm-table", "micromark-extension-gfm-tagfilter", "micromark-extension-gfm-task-list-item", "micromark-util-combine-extensions", "micromark-util-types"]}, "micromark-factory-destination@2.0.1": {"integrity": "sha512-Xe6rDdJlkmbFRExpTOmRj9N3MaWmbAgdpSrBQvCFqhezUn4AHqJHbaEnfbVYYiexVSs//tqOdY/DxhjdCiJnIA==", "dependencies": ["micromark-util-character", "micromark-util-symbol", "micromark-util-types"]}, "micromark-factory-label@2.0.1": {"integrity": "sha512-VFMekyQExqIW7xIChcXn4ok29YE3rnuyveW3wZQWWqF4Nv9Wk5rgJ99KzPvHjkmPXF93FXIbBp6YdW3t71/7Vg==", "dependencies": ["<PERSON>v<PERSON>", "micromark-util-character", "micromark-util-symbol", "micromark-util-types"]}, "micromark-factory-space@2.0.1": {"integrity": "sha512-zRkxjtBxxLd2Sc0d+fbnEunsTj46SWXgXciZmHq0kDYGnck/ZSGj9/wULTV95uoeYiK5hRXP2mJ98Uo4cq/LQg==", "dependencies": ["micromark-util-character", "micromark-util-types"]}, "micromark-factory-title@2.0.1": {"integrity": "sha512-5bZ+3CjhAd9eChYTHsjy6TGxpOFSKgKKJPJxr293jTbfry2KDoWkhBb6TcPVB4NmzaPhMs1Frm9AZH7OD4Cjzw==", "dependencies": ["micromark-factory-space", "micromark-util-character", "micromark-util-symbol", "micromark-util-types"]}, "micromark-factory-whitespace@2.0.1": {"integrity": "sha512-Ob0nuZ3PKt/n0hORHyvoD9uZhr+Za8sFoP+OnMcnWK5lngSzALgQYKMr9RJVOWLqQYuyn6ulqGWSXdwf6F80lQ==", "dependencies": ["micromark-factory-space", "micromark-util-character", "micromark-util-symbol", "micromark-util-types"]}, "micromark-util-character@2.1.1": {"integrity": "sha512-wv8tdUTJ3thSFFFJKtpYKOYiGP2+v96Hvk4Tu8KpCAsTMs6yi+nVmGh1syvSCsaxz45J6Jbw+9DD6g97+NV67Q==", "dependencies": ["micromark-util-symbol", "micromark-util-types"]}, "micromark-util-chunked@2.0.1": {"integrity": "sha512-QUNFEOPELfmvv+4xiNg2sRYeS/P84pTW0TCgP5zc9FpXetHY0ab7SxKyAQCNCc1eK0459uoLI1y5oO5Vc1dbhA==", "dependencies": ["micromark-util-symbol"]}, "micromark-util-classify-character@2.0.1": {"integrity": "sha512-K0kHzM6afW/MbeWYWLjoHQv1sgg2Q9EccHEDzSkxiP/EaagNzCm7T/WMKZ3rjMbvIpvBiZgwR3dKMygtA4mG1Q==", "dependencies": ["micromark-util-character", "micromark-util-symbol", "micromark-util-types"]}, "micromark-util-combine-extensions@2.0.1": {"integrity": "sha512-OnAnH8Ujmy59JcyZw8JSbK9cGpdVY44NKgSM7E9Eh7DiLS2E9RNQf0dONaGDzEG9yjEl5hcqeIsj4hfRkLH/Bg==", "dependencies": ["micromark-util-chunked", "micromark-util-types"]}, "micromark-util-decode-numeric-character-reference@2.0.2": {"integrity": "sha512-ccUbYk6CwVdkmCQMyr64dXz42EfHGkPQlBj5p7YVGzq8I7CtjXZJrubAYezf7Rp+bjPseiROqe7G6foFd+lEuw==", "dependencies": ["micromark-util-symbol"]}, "micromark-util-decode-string@2.0.1": {"integrity": "sha512-nDV/77Fj6eH1ynwscYTOsbK7rR//Uj0bZXBwJZRfaLEJ1iGBR6kIfNmlNqaqJf649EP0F3NWNdeJi03elllNUQ==", "dependencies": ["decode-named-character-reference", "micromark-util-character", "micromark-util-decode-numeric-character-reference", "micromark-util-symbol"]}, "micromark-util-encode@2.0.1": {"integrity": "sha512-c3cVx2y4KqUnwopcO9b/SCdo2O67LwJJ/UyqGfbigahfegL9myoEFoDYZgkT7f36T0bLrM9hZTAaAyH+PCAXjw=="}, "micromark-util-html-tag-name@2.0.1": {"integrity": "sha512-2cNEiYDhCWKI+Gs9T0Tiysk136SnR13hhO8yW6BGNyhOC4qYFnwF1nKfD3HFAIXA5c45RrIG1ub11GiXeYd1xA=="}, "micromark-util-normalize-identifier@2.0.1": {"integrity": "sha512-sxPqmo70LyARJs0w2UclACPUUEqltCkJ6PhKdMIDuJ3gSf/Q+/GIe3WKl0Ijb/GyH9lOpUkRAO2wp0GVkLvS9Q==", "dependencies": ["micromark-util-symbol"]}, "micromark-util-resolve-all@2.0.1": {"integrity": "sha512-VdQyxFWFT2/FGJgwQnJYbe1jjQoNTS4RjglmSjTUlpUMa95Htx9NHeYW4rGDJzbjvCsl9eLjMQwGeElsqmzcHg==", "dependencies": ["micromark-util-types"]}, "micromark-util-sanitize-uri@2.0.1": {"integrity": "sha512-9N9IomZ/YuGGZZmQec1MbgxtlgougxTodVwDzzEouPKo3qFWvymFHWcnDi2vzV1ff6kas9ucW+o3yzJK9YB1AQ==", "dependencies": ["micromark-util-character", "micromark-util-encode", "micromark-util-symbol"]}, "micromark-util-subtokenize@2.1.0": {"integrity": "sha512-XQLu552iSctvnEcgXw6+Sx75GflAPNED1qx7eBJ+wydBb2KCbRZe+NwvIEEMM83uml1+2WSXpBAcp9IUCgCYWA==", "dependencies": ["<PERSON>v<PERSON>", "micromark-util-chunked", "micromark-util-symbol", "micromark-util-types"]}, "micromark-util-symbol@2.0.1": {"integrity": "sha512-vs5t8Apaud9N28kgCrRUdEed4UJ+wWNvicHLPxCa9ENlYuAY31M0ETy5y1vA33YoNPDFTghEbnh6efaE8h4x0Q=="}, "micromark-util-types@2.0.2": {"integrity": "sha512-Yw0ECSpJoViF1qTU4DC6NwtC4aWGt1EkzaQB8KPPyCRR8z9TWeV0HbEFGTO+ZY1wB22zmxnJqhPyTpOVCpeHTA=="}, "micromark@4.0.2": {"integrity": "sha512-zpe98Q6kvavpCr1NPVSCMebCKfD7CA2NqZ+rykeNhONIJBpc1tFKt9hucLGwha3jNTNI8lHpctWJWoimVF4PfA==", "dependencies": ["@types/debug", "debug", "decode-named-character-reference", "<PERSON>v<PERSON>", "micromark-core-commonmark", "micromark-factory-space", "micromark-util-character", "micromark-util-chunked", "micromark-util-combine-extensions", "micromark-util-decode-numeric-character-reference", "micromark-util-encode", "micromark-util-normalize-identifier", "micromark-util-resolve-all", "micromark-util-sanitize-uri", "micromark-util-subtokenize", "micromark-util-symbol", "micromark-util-types"]}, "ms@2.1.3": {"integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA=="}, "remark-frontmatter@5.0.0": {"integrity": "sha512-XTFYvNASMe5iPN0719nPrdItC9aU0ssC4v14mH1BCi1u0n1gAocqcujWUrByftZTbLhRtiKRyjYTSIOcr69UVQ==", "dependencies": ["@types/mdast", "mdast-util-frontmatter", "micromark-extension-frontmatter", "unified"]}, "remark-gfm@4.0.1": {"integrity": "sha512-1quofZ2RQ9EWdeN34S79+KExV1764+wCUGop5CPL1WGdD0ocPpu91lzPGbwWMECpEpd42kJGQwzRfyov9j4yNg==", "dependencies": ["@types/mdast", "mdast-util-gfm", "micromark-extension-gfm", "remark-parse", "remark-stringify", "unified"]}, "remark-parse@11.0.0": {"integrity": "sha512-FCxlKLNGknS5ba/1lmpYijMUzX2esxW5xQqjWxw2eHFfS2MSdaHVINFmhjo+qN1WhZhNimq0dZATN9pH0IDrpA==", "dependencies": ["@types/mdast", "mdast-util-from-markdown", "micromark-util-types", "unified"]}, "remark-stringify@11.0.0": {"integrity": "sha512-1OSmLd3awB/t8qdoEOMazZkNsfVTeY4fTsgzcQFdXNq8ToTN4ZGwrMnlda4K6smTFKD+GRV6O48i6Z4iKgPPpw==", "dependencies": ["@types/mdast", "mdast-util-to-markdown", "unified"]}, "remark@15.0.1": {"integrity": "sha512-Eht5w30ruCXgFmxVUSlNWQ9iiimq07URKeFS3hNc8cUWy1llX4KDWfyEDZRycMc+znsN9Ux5/tJ/BFdgdOwA3A==", "dependencies": ["@types/mdast", "remark-parse", "remark-stringify", "unified"]}, "trough@2.2.0": {"integrity": "sha512-tmMpK00BjZiUyVyvrBK7knerNgmgvcV/KLVyuma/SC+TQN167GrMRciANTz09+k3zW8L8t60jWO1GpfkZdjTaw=="}, "unified@11.0.5": {"integrity": "sha512-xKvGhPWw3k84Qjh8bI3ZeJjqnyadK+GEFtazSfZv/rKeTkTjOJho6mFqh2SM96iIcZokxiOpg78GazTSg8+KHA==", "dependencies": ["@types/unist", "bail", "<PERSON>v<PERSON>", "extend", "is-plain-obj", "trough", "vfile"]}, "unist-util-is@6.0.0": {"integrity": "sha512-2qCTHimwdxLfz+YzdGfkqNlH0tLi9xjTnHddPmJwtIG9MGsdbutfTc4P+haPD7l7Cjxf/WZj+we5qfVPvvxfYw==", "dependencies": ["@types/unist"]}, "unist-util-stringify-position@4.0.0": {"integrity": "sha512-0ASV06AAoKCDkS2+xw5RXJywruurpbC4JZSm7nr7MOt1ojAzvyyaO+UxZf18j8FCF6kmzCZKcAgN/yu2gm2XgQ==", "dependencies": ["@types/unist"]}, "unist-util-visit-parents@6.0.1": {"integrity": "sha512-L/PqWzfTP9lzzEa6CKs0k2nARxTdZduw3zyh8d2NVBnsyvHjSX4TWse388YrrQKbvI8w20fGjGlhgT96WwKykw==", "dependencies": ["@types/unist", "unist-util-is"]}, "unist-util-visit@5.0.0": {"integrity": "sha512-MR04uvD+07cwl/yhVuVWAtw+3GOR/knlL55Nd/wAdblk27GCVt3lqpTivy/tkJcZoNPzTwS1Y+KMojlLDhoTzg==", "dependencies": ["@types/unist", "unist-util-is", "unist-util-visit-parents"]}, "vfile-message@4.0.3": {"integrity": "sha512-QTHzsGd1EhbZs4AsQ20JX1rC3cOlt/IWJruk893DfLRr57lcnOeMaWG4K0JrRta4mIJZKth2Au3mM3u03/JWKw==", "dependencies": ["@types/unist", "unist-util-stringify-position"]}, "vfile@6.0.3": {"integrity": "sha512-KzIbH/9tXat2u30jf+smMwFCsno4wHVdNmzFyL+T/L3UGqqk6JKfVqOFOZEpZSHADH1k40ab6NUIXZq422ov3Q==", "dependencies": ["@types/unist", "vfile-message"]}, "zwitch@2.0.4": {"integrity": "sha512-bXE4cR/kVZhKZX/RjPEflHaKVhUVl85noU3v6b8apfQEc1x4A+zBxjZ4lN8LqGd6WZ3dl98pY4o717VFmoPp+A=="}}}