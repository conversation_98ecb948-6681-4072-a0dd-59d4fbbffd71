{"tasks": {"init": "git config core.hooksPath .githooks", "doctor": "support/bin/doctor.ts", "update-deno-deps": "udd $(find . -type f -not -path \"./support/*\" -regex \".*/deps\\(-test\\)?\\.ts\")", "git-hook-pre-commit": "deno fmt --check --ignore=\"support\" && deno lint  --ignore=\"support\" && deno test --parallel --allow-all --v8-flags=\"--max-old-space-size=4096\"", "git-hook-pre-push": "deno test --parallel --allow-all --v8-flags=\"--max-old-space-size=4096\"", "ts-check": "deno check --no-lock $(find . -name '*.ts' -not -path \"./support/*\")", "prepare-publish": "git semtag final"}, "lint": {"rules": {"exclude": ["no-import-prefix"]}}}